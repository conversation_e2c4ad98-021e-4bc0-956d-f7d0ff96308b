# Heavily edited of <PERSON><PERSON><PERSON> by me into Nopixel 4.0 inspired design with manual transmission.
![Screenshot 2024-01-28 000413](https://github.com/rohKane/Kane-qb-hud-Nopixel-4.0-inspired/assets/47999933/fe1626f3-bad3-4fde-ae74-a9d8acad7a5d)


# Add Manual transmission car
* I've also edit HRSGears into checking vehicle instead of using command to enable it. Here go to HRSGears.lua on line 28 add the car you want.
![7cb42b1df1b2c0d61d1105be1e25f920b6e926b9](https://github.com/rohKane/Kane-qb-hud-Nopixel-4.0-inspired/assets/47999933/ed6fe207-0838-4501-93fc-3139bea1a65e)


# Things you should know:
* To open the menu in-game the **default key** is **"I"** or you can type **"/menu"**
* Your settings will **always** be **stored locally**, so even if you delete the resource it **will** keep your current settings. The **only** way you can wipe the local storage sucessfully is by clicking the **"Reset Settings"** button on the in-game menu
* If you want the new super snazzy sound effects for the menu, you'll need to download the latest update of [interact-sound](https://github.com/qbcore-framework/interact-sound) <br>
(comes with new open menu sound, close menu sound, click menu sound)
* If you receive ```attempt to index a nil value (global 'Lang')``` in your F8 console, the fastest way you can fix that issue is by downloading a new version via txAdminRecipe but if you are unable to do that because of the progress you’ve made on your existing build; you’ll have to unfortunately go through and download each one from the GitHub which I do not recommend. Save yourself a lot of time and pain by just downloading the recipe!

# FAQ
##
**Q:** Why do my borders not align with the maps?

**A:** Most of the time it generally means your safezone is not set to default in your GTA settings. (Settings/Display/"Restore Defaults")
##

##
**Q:** How do I enable dev mode?

**A:** Simple! All you have to do is type /admin and navigate through the menu to the last section called "Developer Options" and inside there you should see "Dev Mode", this will keep you invincible and add a cool developer icon in your circles/radials 
##

##
**Q:** What does the purple circle/radial do?

**A:** That is your harness indicator! When you have the item "harness" in your inventory and while in a vehicle it will appear. Also, when you use your item "harness", the circle/radial will reflect the amount of uses left and decrease overtime.
##
