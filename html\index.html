<html>
    <head>
        <meta id="viewport" name="viewport" content ="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <link rel="stylesheet" type="text/css" href="styles.css"/>
        <link rel="stylesheet" type="text/css" href="responsive.css"/>
        <link rel="stylesheet" href="loading-bar.css">
        <script src="loading-bar.js"></script>
        <link href="https://cdn.jsdelivr.net/npm/quasar@2.1.0/dist/quasar.prod.css" rel="stylesheet" type="text/css"/>
        <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900|Material+Icons" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.13.0/css/all.css"/>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js" defer></script>
        <script src="https://cdn.jsdelivr.net/npm/quasar@2.1.0/dist/quasar.umd.prod.js" defer></script>
        <script src="app.js" defer></script>
    </head>
    <body>
        <div id="baseplate-container" v-show="show">
            <div v-if="showStreets" class="street-container">
                <div class="street2">{{street2}}</div>
                <div class="street1">{{street1}}</div>
            </div>
            <div class="baseplate" v-show="show">
                <div v-if="showPointer" class="pointer">⬥</div>
                <div v-if="showDegrees" class="degrees"></div>
                    <svg class="bezel">
                        <rect width="1" fill="#C8C8C8" height="3" x="-67.5"></rect>
                        <rect width="1" fill="white" height="3" x="-45"></rect>
                        <rect width="1" fill="#C8C8C8" height="3" x="-22.5"></rect>
                        <rect width="1" fill="white" height="3" x="0"></rect>
                        <rect width="1" fill="#C8C8C8" height="3" x="22.5"></rect>
                        <rect width="1" fill="white" height="3" x="45"></rect>
                        <rect width="1" fill="#C8C8C8" height="3" x="67.5"></rect>
                        <rect width="1" fill="white" height="3" x="90"></rect>
                        <rect width="1" fill="#C8C8C8" height="3" x="112.5"></rect>
                        <rect width="1" fill="white" height="3" x="135"></rect>
                        <rect width="1" fill="#C8C8C8" height="3" x="157.5"></rect>
                        <rect width="1" fill="white" height="3" x="180"></rect>
                        <rect width="1" fill="#C8C8C8" height="3" x="202.5"></rect>
                        <rect width="1" fill="white" height="3" x="225"></rect>
                        <rect width="1" fill="#C8C8C8" height="3" x="247.5"></rect>
                    </svg>

                    <svg class="bearing">
                        <text x="0" y="0" dominant-baseline="middle" text-anchor="middle" fill="white">N</text>
                        <text x="180" y="0" dominant-baseline="middle" text-anchor="middle" fill="white">N</text>
                        <text x="157.5" y="0" dominant-baseline="middle" text-anchor="middle" fill="#C8C8C8">NW</text>
                        <text x="-22.5" y="0" dominant-baseline="middle" text-anchor="middle" fill="#C8C8C8">NW</text>
                        <text x="22.5" y="0" dominant-baseline="middle" text-anchor="middle" fill="#C8C8C8">NE</text>
                        <text x="202.5" y="0" dominant-baseline="middle" text-anchor="middle" fill="#C8C8C8">NE</text>
                        <text x="45" y="0" dominant-baseline="middle" text-anchor="middle" fill="white">E</text>
                        <text x="225" y="0" dominant-baseline="middle" text-anchor="middle" fill="white">E</text>
                        <text x="67.5" y="0" dominant-baseline="middle" text-anchor="middle" fill="#C8C8C8">SE</text>
                        <text x="247.5" y="0" dominant-baseline="middle" text-anchor="middle" fill="#C8C8C8">SE</text>
                        <text x="90" y="0" dominant-baseline="middle" text-anchor="middle" fill="white">S</text>
                        <text x="112.5" y="0" dominant-baseline="middle" text-anchor="middle" fill="#C8C8C8">SW</text>
                        <text x="-67.5" y="0" dominant-baseline="middle" text-anchor="middle" fill="#C8C8C8">SW</text>
                        <text x="135" y="0" dominant-baseline="middle" text-anchor="middle" fill="white">W</text>
                        <text x="-45" y="0" dominant-baseline="middle" text-anchor="middle" fill="white">W</text>
                    </svg>
                </div>
            </div>
        <div id="openmenu">
            <div id="menu">
                <q-splitter v-model="splitterModel" style="height: 50vh;">
                    <template v-slot:before>
                        <q-tabs v-model="tab" active-bg-color="active-tab" inline-label indicator-color="transparent" vertical class="text-tabcolor bg-panel">
                            <q-tab name="hud" icon="view_list" label="HUD" style="height: 5.5vh; padding-left: 1vh; justify-content: end;"></q-tab>
                        </q-tabs>
                    </template>
                    <template v-slot:after>
                        <q-tab-panels v-model="tab" animated swipeable vertical transition-prev="jump-up" transition-next="jump-up" class="bg-active-tab">
                            <q-tab-panel name="hud">
                                <div class="q-mb-md">
                                    <div class="text-h6 q-mb-md">Reset Hud <img class="brand-logo" align="right" src="./kane.png"></img></div>
                                    <div class="q-pa-md q-gutter-sm">
                                        <q-btn v-on:click="resetStorage($event)" :loading="progress[0].loading" :percentage="progress[0].percentage" text-color="textbutton" color="bgbutton" @click="startComputing(0)" style="width: 150px"> Reset Settings
                                        <template v-slot:loading> <q-spinner-gears class="on-left"></q-spinner-gears> Resetting... </template></q-btn>
                                        <div class="text-h7 q-mb-md">If you want to reset your settings back to default; click this shiny button! <br> (you will have to relog for your menu to reset changes successfully)</div>
                                        <q-btn v-on:click="restartHud($event)" :loading="progress[1].loading" :percentage="progress[1].percentage" text-color="textbutton" color="bgbutton" @click="startComputing(1)" style="width: 150px"> Reset Hud
                                        <template v-slot:loading> <q-spinner-gears class="on-left"></q-spinner-gears> Resetting... </template></q-btn>
                                        <div class="text-h7 q-mb-md">If your hud is acting up, give it a good ol' reset! Or you can do /resethud</div>
                                    </div>
                                    <hr>
                                    <div class="text-h6 q-mb-md">Options</div>
                                    <div class="text-h7">
                                        <q-item borderless tag="label" v-ripple>
                                            <q-item-section avatar>
                                                <q-checkbox v-model="isOutMapChecked" v-on:click="showOutMap($event)" color="checkbox" val="1"></q-checkbox>
                                            </q-item-section>
                                            <q-item-section>
                                                <q-item-label>Show Minimap Only in Vehicle</q-item-label>
                                                <q-item-label class="text-h7" caption>Disabling this will always keep your minimap on your screen</q-item-label>
                                            </q-item-section>
                                        </q-item>
                                        <q-item borderless tag="label" v-ripple>
                                            <q-item-section avatar>
                                                <q-checkbox v-model="isOutCompassChecked" v-on:click="showOutCompass($event)" color="checkbox" val="2"></q-checkbox>
                                            </q-item-section>
                                            <q-item-section>
                                                <q-item-label>Show Compass Only in Vehicle</q-item-label>
                                                <q-item-label class="text-h7" caption>Disabling this will always keep your compass on your screen</q-item-label>
                                            </q-item-section>
                                        </q-item>
                                        <q-item borderless tag="label" v-ripple>
                                            <q-item-section avatar>
                                                <q-checkbox v-model="isCompassFollowChecked" v-on:click="showFollowCompass($event)" color="checkbox" val="3"></q-checkbox>
                                            </q-item-section>
                                            <q-item-section>
                                                <q-item-label>Show Compass Follow Cam</q-item-label>
                                                <q-item-label class="text-h7" caption>Disabling this will make it so you can no longer use your mouse to rotate the compass around</q-item-label>
                                            </q-item-section>
                                        </q-item>
                                    </div>
                                    <hr>
                                    <div class="text-h6 q-mb-md">Notifications</div>
                                    <div class="text-h7">
                                        <q-checkbox v-on:click="openMenuSounds($event)" label='Menu Sound Effects Enabled' v-model="isOpenMenuSoundsChecked" color="checkbox" val="4" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="resetHudSounds($event)" label='Reset Hud Sound Effects Enabled' v-model="isResetSoundsChecked" color="checkbox" val="5" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="checklistSounds($event)" label='GUI Sound Effects Enabled' v-model="isListSoundsChecked" color="checkbox" val="6" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="showMapNotif($event)" label='Map Notifications Enabled' v-model="isMapNotifChecked" color="checkbox" val="7" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="showFuelAlert($event)" label='Low Fuel Alert Enabled' v-model="isLowFuelChecked" color="checkbox" val="8" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="showCinematicNotif($event)" label='Cinematic Mode Notifications Enabled' v-model="isCinematicNotifChecked" color="checkbox" val="9" style="display: flex;"></q-checkbox>
                                    </div>
                                </div>
                                <hr>
                                <div class="text-h6 q-mb-md">Status</div>
                                <div class="text-h7">
                                    <q-checkbox v-on:click="dynamicHunger($event)" label='Show Hunger always' v-model="isDynamicHungerChecked" color="checkbox" val="12" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicThirst($event)" label='Show Thirst always' v-model="isDynamicThirstChecked" color="checkbox" val="13" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicStress($event)" label='Show Stress always' v-model="isDynamicStressChecked" color="checkbox" val="14" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicOxygen($event)" label='Show Oxygen always' v-model="isDynamicOxygenChecked" color="checkbox" val="15" style="display: flex;"></q-checkbox>
                                </div>
                                <hr>
                                <div class="text-h6 q-mb-md">Vehicle</div>
                                <div class="text-h7">
                                    <q-toggle v-on:click="changeFPS($event)" size="lg" keep-color icon-color="toggleicons" color="checkbox" :label="`Speedometer FPS ${isChangeFPSChecked}`" unchecked-icon="60fps" false-value="Synced" true-value="Optimized" checked-icon="30fps" v-model="isChangeFPSChecked"></q-toggle>
                                    <div class="text-h7 q-mb-md-d">Synced FPS option will result in less optimization, but keep your speedometer in real time, however, it will also be more demanding on your machine.</div>
                                    <q-toggle v-on:click="ToggleMapShape($event)" size="lg" keep-color icon-color="toggleicons" color="checkbox" :label="`Minimap ${isToggleMapShapeChecked}`" unchecked-icon="radio_button_unchecked" false-value="circle" true-value="square" checked-icon="check_box_outline_blank" v-model="isToggleMapShapeChecked"></q-toggle>
                                    <div class="text-h7 q-mb-md-d">Whether it's square or circle you desire, you have the ability to choose!</div>
                                    <q-checkbox v-on:click="HideMap($event)" label='Minimap Enabled' v-model="isHideMapChecked" color="checkbox" val="15" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicEngine($event)" label='Show Engine always' v-model="isDynamicEngineChecked" color="checkbox" val="17" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicNitro($event)" label='Show Nitro always' v-model="isDynamicNitroChecked" color="checkbox" val="18" style="display: flex;"></q-checkbox>
                                </div>
                                <hr>
                                <div class="text-h6 q-mb-md">Compass</div>
                                <div class="text-h7">
                                    <q-toggle v-on:click="changeCompassFPS($event)" size="lg" keep-color icon-color="toggleicons" color="checkbox" :label="`Compass FPS ${isChangeCompassFPSChecked}`" unchecked-icon="60fps" false-value="Synced" true-value="Optimized" checked-icon="30fps" v-model="isChangeCompassFPSChecked"></q-toggle>
                                    <div class="text-h7 q-mb-md-d">Synced FPS option will result in less optimization, but keep your compass in real time, however, it will also be more demanding on your machine.</div>
                                    <q-item borderless tag="label" v-ripple>
                                        <q-item-section avatar>
                                            <q-checkbox v-on:click="showCompassBase($event)" v-model="isShowCompassChecked" color="checkbox" val="19"></q-checkbox>
                                        </q-item-section>
                                        <q-item-section >
                                            <q-item-label>Compass Enabled</q-item-label>
                                            <q-item-label class="text-h7" caption>Disabling this will make it so you can't see the compass navigation</q-item-label>
                                        </q-item-section>
                                    </q-item>
                                    <q-item borderless tag="label" v-ripple>
                                        <q-item-section avatar>
                                            <q-checkbox v-on:click="showStreetsNames($event)" v-model="isShowStreetsChecked" color="checkbox" val="20"></q-checkbox>
                                        </q-item-section>
                                        <q-item-section>
                                            <q-item-label>Show Street Names Enabled</q-item-label>
                                            <q-item-label class="text-h7" caption>Disabling this will make it so you can't see street names / locations</q-item-label>
                                        </q-item-section>
                                    </q-item>
                                    <q-item borderless tag="label" v-ripple>
                                        <q-item-section avatar>
                                            <q-checkbox v-on:click="showPointerIndex($event)" v-model="isPointerShowChecked" color="checkbox" val="21"></q-checkbox>
                                        </q-item-section>
                                        <q-item-section>
                                            <q-item-label>Show Compass Pointer Enabled</q-item-label>
                                            <q-item-label class="text-h7" caption>Disabling this will make it so you can't see your pointer index to pinpoint your exact cardinal directions</q-item-label>
                                        </q-item-section>
                                    </q-item>
                                    <q-item borderless tag="label" v-ripple>
                                        <q-item-section avatar>
                                            <q-checkbox v-on:click="showDegreesNum($event)" v-model="isDegreesShowChecked" color="checkbox" val="22"></q-checkbox>
                                        </q-item-section>
                                        <q-item-section>
                                            <q-item-label>Show Compass Degrees Enabled</q-item-label>
                                            <q-item-label class="text-h7" caption>Disabling this will make it so you can't see your exact degrees</q-item-label>
                                        </q-item-section>
                                    </q-item>
                                </div>
                                <hr>
                                <div class="text-h6 q-mb-md">Cinematic Mode</div>
                                <div class="text-h7">
                                    <q-checkbox v-on:click="cinematicMode($event)" label='Enabled' v-model="isCinematicModeChecked" color="checkbox" val="23" style="display: flex;"></q-checkbox>
                                </div>
                                <br>
                            </q-tab-panel>
                        </q-tab-panels>
                    </template>
                </q-splitter>
            </div>
        </div>
        <div id="main-container">
            <div id="money-container">
                <div id="money-cash">
                    <transition name="slide-fade">
                        <p v-if="showCash"><span id="sign">$&nbsp;</span><span id="money">{{(cash)}}</span></p>
                    </transition>
                </div>
                <div id="money-bank">
                    <transition name="slide-fade">
                        <p v-if="showBank"><span id="sign">$&nbsp;</span><span id="bank">{{(bank)}}</span></p>
                    </transition>
                </div>
                <div id="money-change" v-if="showUpdate">
                    <p v-if="plus" id="money"><span id="plus">+&nbsp;</span><span id="money">{{(amount)}}</span></p>
                    <p v-else-if="minus" id="minus"><span id="minus">-&nbsp;</span><span id="money">{{(amount)}}</span></p>
                </div>
            </div>
            <div id="ui-container">
                <div id="playerHud" v-show="show">
                    <transition name="fade">
                        <div v-show="showRadio" style="position: relative; margin-left: 5px;">
                            <!-- Radio Bar -->
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="var(--radio-bar-color)" data-fill-background-extrude="1" style="width:59px;height:59px;top: 10px;left: -25px; position:absolute; opacity:58%;"></div>
                            <div id="Radio" class="ldBar" data-value :value="radio" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="var(--radio-bar-color)" data-fill-dir="btt" data-fill-background="#84898f"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -20px;"></div>
                            <q-icon id="Icons" style="top: -18px; left: 0px; font-size: 18px;" name="fas fa-headphones" color="white"/>
                        </div>
                    </transition>    
                    <transition name="fade">
                        <div v-if="showVoice" style="position: relative; margin-left: 4px;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="var(--talk-bar-color)" data-fill-background-extrude="1" style="width:59px;height:59px;top: 10px;left: -30px; position:absolute; opacity:58%;"></div>
                            <div id="Voices" class="ldBar" data-value :value="voice" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="var(--talk-bar-color)" data-fill-dir="btt" data-fill-background="#84898f5f"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -25px;"></div>
                            <img src="speaking-head.svg" id="Icons" style="position: absolute; top: 27px; left: -8%; width: 21px;height: 21px;" fill="white"/>
                        </div>
                    </transition>    
                    <transition name="fade">
                        <div v-show="showHealth" id="healthGroup" style="position: relative; margin-left: 4px;">
                            <!-- Health Bar -->
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="#147a47" data-fill-background-extrude="1" style="width:59px;height:59px;top: 10px;left: -35px; position:absolute; opacity:55%;"></div>
                            <div id="Health" class="ldBar" data-value :value="health" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="#20ae67" data-fill-dir="btt" data-fill-background="#20ae6754"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -30px;"></div>
                            <q-icon id="Icons" style="top: -16px; left: -9px; font-size: 17px;" name="fas fa-heart" color="white"/>
                        </div>
                    </transition>                    
                    <transition name="fade">
                        <div v-show="showArmor" id="armorGroup" style="position: relative;margin-left: 4px;;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="#1e4264" data-fill-background-extrude="1" style="width:59px;height:59px;top: 10px;left: -40px; position:absolute; opacity:55%;"></div>
                            <div id="Armor" class="ldBar" data-value :value="armor" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="#216098" data-fill-dir="btt" data-fill-background="#1a436a94"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -35px;"></div>
                            <q-icon id="Icons" style="top: -17px; left: -14px; font-size: 17px;" name="fas fa-shield" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showHunger" id="hungerGroup" style="position: relative;margin-left: 5px;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="#985f31" data-fill-background-extrude="1" style="width:59px;height:59px;top: 10px;left: -45px; position:absolute; opacity:55%;"></div>
                            <div id="Hunger" class="ldBar" data-value :value="hunger" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="#cd7e3e" data-fill-dir="btt" data-fill-background="#985f3182"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -40px;"></div>
                            <q-icon id="Icons" style="top: -18px; left: -19.5px; font-size: 17px;" name="fas fa-hamburger" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showThirst" id="thirstGroup" :style="{ 'margin-left': (showArmor || showHunger) ? '5px' : '4px' }" style="position: relative;">
                            <div class="ldBar"  data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="#0f8585" data-fill-background-extrude="1" style="width:59px;height:59px;top: 10px;left: -50px; position:absolute; opacity:55%;"></div>
                            <div id="Thirst" class="ldBar" data-value :value="thirst" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="#12b9b9" data-fill-dir="btt" data-fill-background="#0f85858f"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -45px;"></div>
                            <q-icon id="Icons" style="top: -18px; left: -24.9px; font-size: 18px;" name="fas fa-tint" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showStress" id="stressGroup" :style="{ 'margin-left': showArmor ? (showHunger ? '5px' : (showThirst ? '8px' : '15px')) : '4px' }" style="position: relative;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="#4f2a32" data-fill-background-extrude="1" style="width:59px;height:59px;top: 10px;left: -55px; position:absolute; opacity:50%;"></div>
                            <div id="Stress" class="ldBar" data-value :value="stress" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="#a0383a" data-fill-dir="btt" data-fill-background="#4f2a3282"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -50px;"></div>
                            <q-icon id="Icons" style="top: -18px; left: -29.5px; font-size: 17px;" name="fas fa-brain" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showOxygen"  id="oxygenGroup" :style="{ 'margin-left': showArmor ? (showThirst ? '9px' : (showStress ? '2px' : (showHunger ? '13px' : '18px'))) : '4px' }" style="position: relative;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="rgb(138, 168, 189)" data-fill-background-extrude="1" style="width:58px;height:58px;top: 10px;left: -60px; position:absolute; opacity:50%;"></div>
                            <div id="Oxygen" class="ldBar" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="rgb(96, 159, 203)" data-fill-dir="btt" data-fill-background="rgba(138, 168, 189, 0.537)"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -55.1px;"></div>
                            <q-icon id="Icons" style="top: -20px; left: -35px; font-size: 18px;" name="fas fa-lungs" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showArmed"  id="armedGroup" :style="{ 'margin-left': showEngine ? '2px' : (showArmor ? (showThirst ? '8px' : (showStress ? '2px' : (showHunger ? '13px' : '18px'))) : '4px') }" style="position: relative;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="rgb(208, 49, 102)" data-fill-background-extrude="1" style="width:58px;height:58px;top: 10px;left: -60px; position:absolute; opacity:30%;"></div>
                            <div id="Armed" class="ldBar" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="rgb(255, 72, 133)" data-fill-dir="btt" data-fill-background="rgb(255, 72, 133)"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -55.1px;"></div>
                            <q-icon id="Icons" style="top: -20px; left: -35px; font-size: 18px;" name="fas fa-stream" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showParachute"  id="parachuteGroup" :style="{ 'margin-left': showEngine ? '2px' : (showArmor ? (showThirst ? '8px' : (showStress ? '2px' : (showHunger ? '13px' : '18px'))) : '4px') }" style="position: relative;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="rgb(0, 0, 0)" data-fill-background-extrude="1" style="width:58px;height:58px;top: 10px;left: -60px; position:absolute; opacity:30%;"></div>
                            <div id="Parachute" class="ldBar" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="rgb(0, 0, 0)" data-fill-dir="btt" data-fill-background="rgb(0, 0, 0)"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -55.1px;"></div>
                            <q-icon id="Icons" style="top: -20px; left: -35px; font-size: 18px;" name="fas fa-parachute-box" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showEngine"  id="engineGroup" :style="{ 'margin-left': showArmor ? (showThirst ? '9px' : (showStress ? '2px' : (showHunger ? '13px' : '18px'))) : '4px' }" style="position: relative;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="#29be7e" data-fill-background-extrude="1" style="width:58px;height:58px;top: 10px;left: -60px; position:absolute; opacity:30%;"></div>
                            <div id="Engine" class="ldBar" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="#29be7e" data-fill-dir="btt" data-fill-background="#29be7d3f"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -55.1px;"></div>
                            <q-icon id="Icons" style="top: -20px; left: -35px; font-size: 18px;" name="fas fa-oil-can" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showHarness"  id="harnessGroup" :style="{ 'margin-left': showEngine ? '2px' : (showArmor ? (showThirst ? '8px' : (showStress ? '2px' : (showHunger ? '13px' : '18px'))) : '4px') }" style="position: relative;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="rgba(182, 72, 255, 0.540)" data-fill-background-extrude="1" style="width:58px;height:58px;top: 10px;left: -60px; position:absolute; opacity:50%;"></div>
                            <div id="Harness" class="ldBar" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="rgb(182, 72, 255)" data-fill-dir="btt" data-fill-background="rgba(182, 72, 255, 0.540)"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -55.1px;"></div>
                            <q-icon id="Icons" style="top: -20px; left: -35px; font-size: 18px;" name="fas fa-user-slash" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showCruise"  id="cruiseGroup" :style="{ 'margin-left': showEngine ? '2px' : (showArmor ? (showThirst ? '8px' : (showStress ? '2px' : (showHunger ? '13px' : '18px'))) : '4px') }" style="position: relative;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="rgba(132, 195, 54, 0.529)" data-fill-background-extrude="1" style="width:58px;height:58px;top: 10px;left: -60px; position:absolute; opacity:50%;"></div>
                            <div id="Cruise" class="ldBar" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="rgb(131, 195, 54)" data-fill-dir="btt" data-fill-background="rgba(132, 195, 54, 0.529)"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -55.1px;"></div>
                            <q-icon id="Icons" style="top: -20px; left: -35px; font-size: 18px;" name="fas fa-tachometer-alt-fast" color="white"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showNos"  id="nosGroup" :style="{ 'margin-left': showEngine ? '2px' : (showArmor ? (showThirst ? '8px' : (showStress ? '2px' : (showHunger ? '13px' : '18px'))) : '4px') }" style="position: relative;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="rgba(29, 136, 186, 0.565)" data-fill-background-extrude="1" style="width:58px;height:58px;top: 10px;left: -60px; position:absolute; opacity:50%;"></div>
                            <div id="Nos" data-value :value="nos" class="ldBar" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="rgb(29, 136, 186)" data-fill-dir="btt" data-fill-background="rgba(29, 136, 186, 0.352)"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -55.1px;"></div>
                            <q-icon id="Icons" style="top: -20px; left: -35px; font-size: 18px;" name="fas fa-meteor" :style="{color: nosColor}"/>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-show="showDev"  id="devGroup" :style="{ 'margin-left': showEngine ? '2px' : (showArmor ? (showThirst ? '8px' : (showStress ? '2px' : (showHunger ? '13px' : '18px'))) : '4px') }" style="position: relative;">
                            <div class="ldBar" data-value="0" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill-dir="btt" data-fill-background="rgb(0, 0, 0)" data-fill-background-extrude="1" style="width:58px;height:58px;top: 10px;left: -60px; position:absolute; opacity:50%;"></div>
                            <div id="Dev" data-value :value="nos" class="ldBar" data-type="fill" data-path="M10 30 L45 10 L80 30 L80.5 70 L45 90 L10 70 Z" data-fill="rgb(0, 0, 0)" data-fill-dir="btt" data-fill-background="rgb(0, 0, 0)"  data-fill-background-extrude="0" style="width:48px;height:48px;top: 15px; left: -55.1px;"></div>
                            <q-icon id="Icons" style="top: -20px; left: -35px; font-size: 18px;" name="fas fa-terminal" color="white"/>
                        </div>
                    </transition>
                </div>
            </div>
            <div id="veh-container">
                <div v-show="show">                    
                    <div class="responsive" id="speedometer">
                        <div id="rpmBar" class="container" style="width: 100%; left: -13%;top: 20%;"></div>
                        <div id="Speed" style="transform: scale(2.7) rotate(0deg); left: 46%; top: -150%;" v-html="speedDisplay"></div>
                        <div id="gearBox" style="top: -182%;left: -9%;">
                        <div id="gear" style="text-align: center; position: absolute; left: -85%; bottom: -92%; width: 100px; height: 100px;">0</div>
                        <q-icon id="Icons" style="top: 2.6vh; left: 0.9vh; font-size: 17px;" name="fas fa-cogs" color="black"/>
                        <!-- This is the html file for the number hud -->
                        </div>
                    </div>
                    <div class="responsive" id="fuelgauge">
                        <div id="progressBar" style="left: 870%; top: 90%;">
                            <div class="progress" style="height: 0;">
                        </div>
                        <q-icon id="FuelGaugeIcon" name="fas fa-gas-pump" style="font-size: 15px;left: -0.10vw; top: -2.5vh;" color="white"/>
                    </div>
                    <div class="responsive" id="altitudegauge" v-if="showAltitude">
                        <q-circular-progress id="Altimeter" class="q-ml-xl" style="transform: rotate(-135deg); position: absolute; opacity: 60%; left: -49vh; top: -3vh;" :value="altitudegauge" size="70px" :thickness="0.21" color="gauge" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress id="AltimeterValue" class="q-ml-xl" style="transform: rotate(-135deg); left: -49vh; top: -3vh;" show-value :value="altitude" size="70px" :thickness="0.21" color="gauge" :min="0" :max="750">
                        <altitude id="Alt">{{(altitude)}}</altitude>
                    </div>
                    <transition name="fade">
                    <div id="seatbelt" v-if="showSeatbelt">
                        <img id="SeatbeltIcon" src="seatbelt.png" :value="seatbelt"/>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
