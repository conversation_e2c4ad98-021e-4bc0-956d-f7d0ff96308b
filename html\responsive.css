.responsive {
    position: absolute !important;
}

@media (min-width: 800px) {
    .responsive {
        margin-left: 32vh !important;
        transform: scale(1, 1);
        bottom: 6.9vh !important;
    }
    .q-ml-xl {
        font-size: 50px;
    }
    #Speedo {
        font-size: 70px !important;
    }
    #FuelGaugeBackground {
        font-size: 36px;
    }
    #FuelGaugeValue {
        font-size: 36px;
    }
    #FuelGaugeIcon {
        font-size: 14px;
    }
    #Icons {
        font-size: 19.5px;
    }
    #speedometer {
        position: relative;
        left: 2.5vh !important;
    }
    #fuelgauge {
        position: relative;
        left: 7vh !important;
        bottom: 6.7vh !important;
    }
    #altitudegauge {
        position: relative;
        left: 11vh !important;
    }
    #seatbelt {
        position: relative;
        left: 8.5vh !important;
    }
    speed {
        font-size: 2vh !important;
    }
    speed:after {
        font-size: 0.9vh !important;
    }
    altitude {
        font-size: 2vh !important;
    }
    altitude:after {
        font-size: 0.9vh !important;
    }
    .square {
        left: 2.5vh !important;
        bottom: 6% !important;
    }
    .circle {
        left: 3.2vh !important;
        width: 27.6vh !important;
        bottom: 6.9% !important;
    }
}

@media (width: 3840px) and (height: 2160px) {
    .responsive {
        margin-left: 32.8vh !important;
        transform: scale(1.3, 1.3);
        bottom: 7.6vh !important;
    }
    .q-ml-xl {
        font-size: 100px;
        margin-left: -45px !important; /* Change this to space radials */
    }
    #Speedo {
        font-size: 125px !important;
    }
    #Speed {
        font-size: 50px !important;
    }
    #FuelGaugeBackground {
        font-size: 75px !important;
        left: 50% !important;
        top: 20px !important;
    }
    #FuelGaugeValue {
        font-size: 75px !important;
        left: 0% !important;
        top: 20px !important;
    }
    #FuelGaugeIcon {
        font-size: 25px;
    }
    #Altimeter {
        font-size: 125px !important;
        left: 41% !important;
    }
    #AltimeterValue {
        font-size: 125px !important;
        left: -9% !important;
    }
    #Alt {
        font-size: 50px !important;
    }
    #Icons {
        font-size: 50px;
    }
    #SeatbeltLocation {
        left: 35% !important;
        top: -40px;
    }
    #SeatbeltIcon {
        font-size: 40px !important;
    }
    #speedometer {
        position: relative;
        left: 2.5vh !important;
    }
    #fuelgauge {
        position: relative;
        left: 5.3vh !important;
        bottom: 7.1vh !important;
    }
    #altitudegauge {
        position: relative;
        left: 8.2vh !important;
    }
    #seatbelt {
        position: relative;
        left: 6.5vh !important;
    }
    speed {
        font-size: 1.2vh !important;
    }
    speed:after {
        font-size: 0.6vh !important;
    }
    altitude {
        font-size: 1.2vh !important;
    }
    altitude:after {
        font-size: 0.6vh !important;
    }
    .square {
        left: 2.4vh !important;
        bottom: 6.1% !important;
        width: 29.3vh !important;
    }
    .circle {
        left: 3.6vh !important;
        width: 27.6vh !important;
        bottom: 6.9% !important;
    }
}

@media (width: 3440px) and (height: 1440px) {
    .responsive {
        margin-left: 33vh !important;
        transform: scale(1.3, 1.3);
        bottom: 7.6vh !important;
    }
    .q-ml-xl {
        font-size: 50px;
    }
    #Speedo {
        font-size: 70px !important;
    }
    #FuelGaugeBackground {
        font-size: 36px;
    }
    #FuelGaugeValue {
        font-size: 36px;
    }
    #FuelGaugeIcon {
        font-size: 14px;
    }
    #Icons {
        font-size: 19.5px;
    }
    #speedometer {
        position: relative;
        left: 2.5vh !important;
    }
    #fuelgauge {
        position: relative;
        left: 6.4vh !important;
        bottom: 6.85vh !important;
    }
    #altitudegauge {
        position: relative;
        left: 11.2vh !important;
    }
    #seatbelt {
        position: relative;
        left: 4vh !important;
    }
    speed {
        font-size: 1.4vh !important;
    }
    speed:after {
        font-size: 0.7vh !important;
    }
    altitude {
        font-size: 1.4vh !important;
    }
    altitude:after {
        font-size: 0.7vh !important;
    }
    .square {
        left: 2.3vh !important;
        bottom: 6.1% !important;
        width: 29.3vh !important;
    }
    .circle {
        left: 3.5vh !important;
        width: 27.6vh !important;
        bottom: 6.9% !important;
    }
}

@media (width: 2560px) and (height: 1440px) {
    .responsive {
        margin-left: 33.5vh !important;
        transform: scale(1.3, 1.3);
        bottom: 7.2vh !important;
    }
    .q-ml-xl {
        font-size: 50px;
    }
    #Speedo {
        font-size: 70px !important;
    }
    #FuelGaugeBackground {
        font-size: 36px;
    }
    #FuelGaugeValue {
        font-size: 36px;
    }
    #FuelGaugeIcon {
        font-size: 14px;
    }
    #Icons {
        font-size: 19.5px;
    }
    #speedometer {
        position: relative;
        left: 2.5vh !important;
    }
    #fuelgauge {
        position: relative;
        left: 6.3vh !important;
        bottom: 6.5vh !important;
    }
    #altitudegauge {
        position: relative;
        left: 11vh !important;
    }
    #seatbelt {
        position: relative;
        left: 4.8vh !important;
    }
    speed {
        font-size: 1.4vh !important;
    }
    speed:after {
        font-size: 0.7vh !important;
    }
    altitude {
        font-size: 1.4vh !important;
    }
    altitude:after {
        font-size: 0.7vh !important;
    }
    .square {
        left: 2.3vh !important;
        bottom: 6% !important;
    }
    .circle {
        left: 3.5vh !important;
        width: 27.6vh !important;
        bottom: 6.7% !important;
    }
}

@media (width: 2560px) and (height: 1080px) {
    #speedometer {
        position: relative;
        left: 2.5vh !important;
    }
    #fuelgauge {
        position: relative;
        left: 6.5vh !important;
        bottom: 5.7vh !important;
    }
    #seatbelt {
        position: relative;
        left: 6.5vh !important;
    }
    .square {
        left: 2.5vh !important;
        bottom: 6% !important;
    }
    .circle {
        left: 3.2vh !important;
        width: 27.6vh !important;
        bottom: 6.9% !important;
    }
}

@media (width: 1920px) and (height: 1440px) {
    .responsive {
        margin-left: 32vh !important;
        transform: scale(1.2, 1.2);
        bottom: 7.4vh !important;
    }
    .q-ml-xl {
        font-size: 50px;
    }
    #Speedo {
        font-size: 70px !important;
    }
    #FuelGaugeBackground {
        font-size: 36px;
    }
    #FuelGaugeValue {
        font-size: 36px;
    }
    #FuelGaugeIcon {
        font-size: 14px;
    }
    #Icons {
        font-size: 19.5px;
    }
    #speedometer {
        position: relative;
        left: 2.5vh !important;
    }
    #fuelgauge {
        position: relative;
        left: 6.3vh !important;
        bottom: 6.9vh !important;
    }
    #altitudegauge {
        position: relative;
        left: 10vh !important;
    }
    #seatbelt {
        position: relative;
        left: 8vh !important;
    }
    speed {
        font-size: 1.6vh !important;
    }
    speed:after {
        font-size: 0.7vh !important;
    }
    altitude {
        font-size: 1.6vh !important;
    }
    altitude:after {
        font-size: 0.7vh !important;
    }
    .square {
        left: 2vh !important;
        bottom: 6% !important;
    }
    .circle {
        left: 2.7vh !important;
        width: 27.6vh !important;
        bottom: 7% !important;
    }
}

@media (width: 1920px) and (height: 1200px) {
    .responsive {
        margin-left: 31.5vh !important;
        transform: scale(1, 1);
        bottom: 6.4vh !important;
    }
    .q-ml-xl {
        font-size: 50px;
    }
    #Speedo {
        font-size: 70px !important;
    }
    #FuelGaugeBackground {
        font-size: 36px;
    }
    #FuelGaugeValue {
        font-size: 36px;
    }
    #FuelGaugeIcon {
        font-size: 14px;
    }
    #Icons {
        font-size: 19.5px;
    }
    #speedometer {
        position: relative;
        left: 2.5vh !important;
    }
    #fuelgauge {
        position: relative;
        left: 6.6vh !important;
        bottom: 6.05vh !important;
    }
    #altitudegauge {
        position: relative;
        left: 10vh !important;
    }
    #seatbelt {
        position: relative;
        left: 8vh !important;
    }
    speed {
        font-size: 1.8vh !important;
    }
    speed:after {
        font-size: 0.8vh !important;
    }
    altitude {
        font-size: 1.8vh !important;
    }
    altitude:after {
        font-size: 0.8vh !important;
    }
    .square {
        left: 2.2vh !important;
        bottom: 6% !important;
    }
    .circle {
        left: 3.2vh !important;
        width: 27.6vh !important;
        bottom: 7% !important;
    }
}

@media (width: 1920px) and (height: 1080px) {
    .responsive {
        margin-left: 32vh !important;
        transform: scale(1, 1);
        bottom: 6.9vh !important;
    }
    .q-ml-xl {
        font-size: 50px;
    }
    #Speedo {
        font-size: 70px !important;
    }
    #FuelGaugeBackground {
        font-size: 36px;
    }
    #FuelGaugeValue {
        font-size: 36px;
    }
    #FuelGaugeIcon {
        font-size: 14px;
    }
    #Icons {
        font-size: 19.5px;
    }
    #speedometer {
        position: relative;
        left: 2.5vh !important;
    }
    #fuelgauge {
        position: relative;
        left: 7.2vh !important;
        bottom: 6.6vh !important;
    }
    #altitudegauge {
        position: relative;
        left: 11vh !important;
    }
    #seatbelt {
        position: relative;
        left: -5.6vh !important;
    }
    speed {
        font-size: 2vh !important;
    }
    speed:after {
        font-size: 1.2vh !important;
    }
    altitude {
        font-size: 2vh !important;
    }
    altitude:after {
        font-size: 0.9vh !important;
    }
    .square {
        left: 2.5vh !important;
        bottom: 6% !important;
    }
    .circle {
        left: 3.7vh !important;
        width: 27.3vh !important;
        bottom: 7% !important;
    }
}

@media (width: 1280px) and (height: 720px) {
    .responsive {
        margin-left: 30vh !important;
        transform: scale(0.65, 0.65);
        bottom: 4.7vh !important;
    }
    .q-ml-xl {
        font-size: 50px;
    }
    #Speedo {
        font-size: 70px !important;
    }
    #FuelGaugeBackground {
        font-size: 36px;
    }
    #FuelGaugeValue {
        font-size: 36px;
    }
    #FuelGaugeIcon {
        font-size: 14px;
    }
    #Icons {
        font-size: 19.5px;
    }
    #speedometer {
        position: relative;
        left: 2.5vh !important;
    }
    #fuelgauge {
        position: relative;
        left: 8.6vh !important;
        bottom: 5.2vh !important;
    }
    #altitudegauge {
        position: relative;
        left: 11vh !important;
    }
    #seatbelt {
        position: relative;
        left: 8.5vh !important;
    }
    speed {
        font-size: 3vh !important;
    }
    speed:after {
        font-size: 1.4vh !important;
    }
    altitude {
        font-size: 3vh !important;
    }
    altitude:after {
        font-size: 1.4vh !important;
    }
    .square {
        left: 2.5vh !important;
        bottom: 6% !important;
    }
    .circle {
        left: 4vh !important;
        width: 27.6vh !important;
        bottom: 6.9% !important;
    }
}
